/* THEME & LAYOUT VARIABLES */
:root {
  /* Colors */
  --primary-brand: #5b6eff;
  --primary-brand-hover: #4a59e0;
  --primary-gradient: linear-gradient(to right, #5b6eff, #ec6fcd);
  --secondary-gradient: linear-gradient(to right, #2196f3, #03a9f4);

  --success: #4caf50;
  --success-dark: #388e3c;
  --success-bg: rgba(76, 175, 80, 0.1);
  --success-border: rgba(76, 175, 80, 0.3);

  --danger: #f44336;
  --danger-dark: #d32f2f;
  --danger-gradient: linear-gradient(to right, #f44336, #ff9800);
  --danger-text: #ff9a9a;
  --danger-bg: rgba(255, 0, 0, 0.15);
  --danger-border: rgba(255, 0, 0, 0.25);

  --warning: #f59e0b;
  --warning-bg: #451a03;
  --warning-text: #fed7aa;

  --blue: #2196f3;
  --blue-dark: #1976d2;

  --toast-success-bg: #1b5e20;
  --toast-error-bg: #b71c1c;

  --text-primary: #fff;
  --text-secondary: #aaa;
  --text-muted: #666;
  --text-link: var(--primary-brand);
  --text-link-hover: #8a9bff;
  --client-id: #ffeb3b;

  --bg-main: #121212;
  --bg-card: #1e1e1e;
  --bg-input: #2a2a2a;
  --bg-hover: rgba(255, 255, 255, 0.05);
  --bg-active: #2d2d3a;

  --border-primary: #333;
  --border-secondary: #444;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;

  /* Radius */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
}

/* BASE & GLOBAL STYLES */
html,
body {
  margin: 0;
  padding: 0;
  background-color: #000;
  color: #fff;
  box-sizing: border-box;
  width: 100%;
  background-color: var(--bg-main);
  color: var(--text-primary);
  font-family: 'Segoe UI', sans-serif;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

.admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-md) var(--space-md) var(--space-xl) var(--space-md);
}

/* HEADER */
.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
  padding: 0.75rem;
}

.admin-title-container {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.admin-logo {
  height: 28px;
  width: auto;
  pointer-events: none;
}

.admin-title {
  font-size: 1.1rem;
  font-weight: 400;
  margin: 0;
}

.admin-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* CARDS */
.admin-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-sm);
  padding: var(--space-md);
  margin-bottom: var(--space-md);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.admin-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.admin-card-title {
  font-size: 1rem;
  font-weight: 400;
  margin-top: 0;
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
}

.admin-card-header .admin-card-title {
  margin-bottom: 0;
}

.admin-card-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-top: 0.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* TABS */
.admin-tabs {
  display: flex;
  gap: var(--space-xs);
}

.admin-tab {
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  border-bottom: none;
}

.admin-tab:hover {
  color: var(--text-primary);
  background-color: var(--bg-hover);
}

.admin-tab.active {
  color: var(--text-primary);
  background-color: var(--bg-card);
  border-color: var(--border-primary);
  border-bottom-color: var(--bg-input);
  position: relative;
  top: 1px;
}

.admin-tab-content {
  min-height: 200px;
}

/* STATS & TABLES */
.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.stat-description {
  font-size: 0.75rem;
  color: #888;
  margin-top: 0.25rem;
}

.stats-summary {
  background-color: var(--bg-input);
  border-radius: var(--radius-lg);
  padding: 0.6rem 0.75rem;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  white-space: nowrap;
}

.stat-card {
  background-color: var(--bg-input);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin: var(--space-sm) 0;
  color: var(--primary-brand);
}

.stat-value-compact {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-brand);
}

.stat-label {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.client-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0.75rem;
  table-layout: auto;
}

.client-table th,
.client-table td {
  padding: 0.2rem 0.1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
  font-size: 0.8rem;
}

.client-table th {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.client-table tr:hover {
  background-color: var(--bg-hover);
}

.id-cell,
.status-cell,
.sync-cell {
  text-align: center;
}

.client-id {
  color: var(--client-id);
  font-weight: 700;
}

.status-enabled {
  color: var(--success);
}

.status-disabled {
  color: var(--danger);
}

.status-combined {
  display: flex;
  align-items: center;
  gap: 2px;
  white-space: nowrap;
  justify-content: center;
}

.status-separator {
  color: var(--text-muted);
  font-size: 0.7rem;
  margin: 0 3px;
}

.sync-status {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  min-width: 60px;
}

.sync-status::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

/* FORMS & INPUTS */
.login-container {
  max-width: 400px;
  margin: 100px auto;
  padding: var(--space-md);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.form-group {
  margin-bottom: var(--space-md);
}

.form-label {
  display: block;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 500;
}

input,
textarea,
.form-input {
  width: 100%;
  padding: 0.8rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  background-color: var(--bg-input);
  color: var(--text-primary);
  font-size: 1rem;
}

.config-textarea {
  padding: 0.8rem;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-brand);
}

.form-error {
  color: var(--danger-text);
  background-color: var(--danger-bg);
  border: 1px solid var(--danger-border);
  padding: 0.75rem;
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-md);
  text-align: center;
  font-size: 0.9rem;
}

/* BUTTONS */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.9rem;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  text-decoration: none;
  color: var(--text-primary);
  background: var(--primary-gradient);
}

.button:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* From admin/notifications page */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: white;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #5b6eff;
    box-shadow: 0 0 0 3px rgba(91, 110, 255, 0.1);
}

select.form-input {
    background-color: #2a2a2a;
    color: white;
    cursor: pointer;
}

select.form-input option {
    background-color: #2a2a2a;
    color: white;
}

textarea.form-input {
    min-height: 100px;
    resize: vertical;
}

small {
    display: block;
    color: #666;
    font-size: 0.85em;
    margin-top: 0.25rem;
}

button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    background-color: #5b6eff;
    color: white;
}

button:hover:not(:disabled) {
    background-color: #4c5fd7;
    transform: translateY(-1px);
}

button:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.button:active:not(:disabled) {
  transform: translateY(0);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button Modifiers */
.button.button-secondary {
  background: var(--border-primary);
}

.button.button-success {
  background: var(--success);
}
.button.button-success:hover:not(:disabled) {
  background: var(--success-dark);
}

.button.button-danger {
  background: var(--danger-gradient);
}

.button.button-blue {
  background: var(--secondary-gradient);
}

.button.button-small {
  padding: 0.3rem 0.5rem;
  font-size: 0.8rem;
  min-width: 55px;
}

.button.button-cancel {
  background: #444;
}
.button.button-cancel:hover {
  background: #555;
}

/* Client-specific Buttons */
.client-toggle-btn {
  padding: 0.1rem 0.25rem;
  font-size: 0.65rem;
  min-width: 40px;
  border-radius: var(--radius-sm);
  font-weight: 500;
  box-shadow: none;
}

.client-btn-on {
  background: var(--success) !important;
}

.client-btn-off {
  background: var(--danger) !important;
}

.client-trust-btn {
  padding: 0.1rem 0.25rem;
  font-size: 0.65rem;
  min-width: 40px;
  background: var(--border-primary);
  border-radius: var(--radius-sm);
  font-weight: 500;
  box-shadow: none;
}

/* MISC & UTILITY */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: #aaa;
}

.timestamp {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.75rem;
  text-align: right;
}

.refresh-countdown {
  color: var(--primary-brand);
  margin-left: var(--space-sm);
  font-weight: 500;
}

.empty-state {
  text-align: center;
  padding: var(--space-lg) var(--space-md);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.text-link {
  color: var(--text-link);
  text-decoration: none;
  transition: color 0.2s ease;
}

.text-link:hover {
  color: var(--text-link-hover);
  text-decoration: underline;
}

/* TOAST MESSAGES */
.toast-container {
  position: fixed;
  bottom: calc(10px + env(safe-area-inset-bottom));
  left: 0;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: none;
  padding: 0 10px;
}

.toast-message {
  background-color: var(--toast-success-bg);
  color: var(--text-primary);
  padding: 12px 16px;
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 600px;
  text-align: center;
  pointer-events: auto;
  opacity: 1 !important;
  transform: translateY(0) !important;
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
  margin-bottom: 10px;
  font-weight: 500;
  word-wrap: break-word;
}

.toast-message.toast-error {
  background-color: var(--toast-error-bg);
}

/* APK REPACKER & FILE UPLOAD */
.apk-repacker-container {
  padding: var(--space-md) 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.download-all-button {
  animation: pulse 1.5s infinite;
  --pulse-color: var(--blue);
}

.download-all-button:hover {
  animation: none;
}

.download-ready-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--success);
  animation: fadeIn 0.5s ease-in-out;
  background-color: var(--success-bg);
  padding: 5px 10px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--success-border);
}

.download-ready-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--success);
  animation: pulse 1.5s infinite;
  --pulse-color: var(--success);
}

/* KEYFRAMES */
@keyframes pulse {
  0%, 100% {
    transform: scale(0.98);
    box-shadow: 0 0 0 0 rgba(var(--pulse-color, 0, 0, 0), 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(var(--pulse-color, 0, 0, 0), 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .admin-header {
    /* Keep row layout instead of column */
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
  }

  /* Mobile tab styles */
  .admin-tabs {
    overflow-x: auto;
    white-space: nowrap;
    gap: 0.1rem;
  }

  .admin-tab {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }

  .admin-title-container {
    flex-shrink: 1;
  }

  .admin-title {
    font-size: 0.95rem; /* Slightly smaller title on mobile */
    pointer-events: none;
  }

  .admin-actions {
    flex-shrink: 0;
    gap: 0.4rem;
  }

  .admin-card {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .admin-stats {
    grid-template-columns: 1fr;
  }

  /* Keep stats-summary horizontal on mobile as well */
  .stats-summary {
    justify-content: space-around;
    padding: 0.5rem 0.4rem;
  }

  .stat-item {
    gap: 0.3rem;
  }

  .stat-value-compact {
    font-size: 1.1rem;
  }

  .client-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .client-table th,
  .client-table td {
    padding: 0.4rem 0.5rem;
    font-size: 0.75rem;
  }

  .client-table th {
    font-size: 0.7rem;
  }

  /* Adjust column widths for mobile */
  .client-table th:nth-child(1),
  .client-table td:nth-child(1) {
    width: 70px;
    min-width: 70px;
  }

  .client-table th:nth-child(2),
  .client-table td:nth-child(2) {
    width: 60px;
    min-width: 60px;
  }

  .client-table th:nth-child(3),
  .client-table td:nth-child(3) {
    width: 80px;
    min-width: 80px;
  }

  .client-table th:nth-child(4),
  .client-table td:nth-child(4) {
    width: 80px;
    min-width: 80px;
  }

  .client-table th:nth-child(6),
  .client-table td:nth-child(6) {
    width: 60px;
    min-width: 60px;
  }

  /* Make buttons more compact on mobile */
  .button {
    /* padding: 0.3rem 0.5rem; */
    font-size: 0.7rem;
  }

  .button-small {
    /* padding: 0.2rem 0.4rem; */
    font-size: 0.1rem;
    /* min-width: 50px; */
  }

  .empty-state {
    padding: 0.5rem 0rem;
  }

  .timestamp {
    font-size: 0.7rem;
    margin-top: 0.5rem;
  }

  .horizontal-layout {
    flex-direction: column;
  }

  .file-drop-area {
    padding: 1rem;
    min-height: 100px;
  }

  .config-textarea {
    min-height: 160px;
  }

  .selected-file {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
  }

  .file-name {
    max-width: 200px;
  }

  .form-actions {
    justify-content: stretch;
    flex-wrap: wrap;
    margin-top: 0rem;
  }

  .download-actions {
    flex-direction: column;
    width: 100%;
    gap: 0.5rem;
  }

  .button,
  .download-button,
  .reset-button {
    width: 100%;
    padding: 0.6rem 1rem;
    margin-top: 0.5rem;
  }

  .danger-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .confirmation-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .danger-section {
    padding: 0.75rem;
    margin-top: 1.5rem;
  }
}

/* APK Repacker Styles */
.config-textarea {
  width: 100%;
  min-height: 120px;
  padding: var(--space-sm);
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.9rem;
  resize: vertical;
}

.asset-filename-input {
  min-height: auto;
  height: 40px;
  font-family: 'Segoe UI', sans-serif;
}

.asset-filename-container {
  margin-top: 10px;
}

.asset-filename-input {
  min-height: unset !important;
  height: auto !important;
  padding: 8px !important;
  margin-bottom: 0 !important;
  resize: none !important;
}

.asset-filename-help {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.apk-repacker-container {
  padding: 1rem 0;
}

.horizontal-layout {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  gap: 1rem;
}

.horizontal-layout .form-group {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
}

.file-upload-group {
  display: flex;
  flex-direction: column;
}

.admin-card-description {
  color: #aaa;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.file-drop-area {
  border: 1px solid #444;
  border-radius: 4px;
  padding: 1rem;
  text-align: center;
  background-color: #2a2a2a;
  transition: all 0.2s ease;
  cursor: pointer;
  margin-bottom: 0;
  height: 100%;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-drop-area.active {
  border-color: #5b6eff;
  background-color: #2d2d3a;
}

.file-drop-area.has-file {
  border-color: #5b6eff;
  background-color: #2a2a2a;
}

.drop-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
}

.file-upload-area {
  border: 2px dashed var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
  margin-top: var(--space-lg);
}

.file-upload-area:hover,
.file-upload-area.drag-over {
  border-color: var(--primary-brand);
  background-color: var(--bg-secondary);
}

.file-upload-area p {
  margin: 0;
  color: var(--text-secondary);
}

.file-list {
  margin-top: var(--space-lg);
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-sm);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-sm);
}

.file-name {
  font-weight: 500;
}

.remove-file-btn {
  background: none;
  border: none;
  color: var(--danger);
  cursor: pointer;
  font-size: 1rem;
}

/* TEAM MANAGEMENT */
.team-management-container {
  margin-top: var(--space-xl);
}

.team-member-list {
  list-style: none;
  padding: 0;
}

.team-member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) 0;
  border-bottom: 1px solid var(--border-primary);
}

.team-member-item:last-child {
  border-bottom: none;
}

.team-member-details {
  font-size: 0.9rem;
}

.team-member-name {
  font-weight: 600;
  color: var(--text-primary);
}

.team-member-email {
  color: var(--text-secondary);
  margin-left: var(--space-sm);
}

.team-member-actions .button {
  margin-left: var(--space-sm);
}

/* DANGER ZONE */
.danger-zone {
  margin-top: var(--space-xl);
  border: 2px solid var(--danger);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  background-color: var(--danger-bg);
}

.danger-zone h3 {
  color: var(--danger);
  margin-top: 0;
}

.danger-zone p {
  color: var(--danger-dark);
  font-size: 0.9rem;
}

/* RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .admin-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    position: static;
  }

  .main-content {
    margin-left: 0;
    padding: var(--space-md);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .client-table th,
  .client-table td {
    font-size: 0.8rem;
    padding: var(--space-xs);
  }

  .client-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }

  .client-actions .button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    padding: var(--space-sm);
    gap: var(--space-sm);
  }

  .tabs {
    width: 100%;
    justify-content: space-around;
  }

  .tab-link {
    padding: var(--space-sm);
    font-size: 0.85rem;
  }

  .button {
    padding: 0.7rem;
    font-size: 0.9rem;
  }
}

.text-link {
  color: var(--primary-brand);
  text-decoration: none;
  transition: color 0.2s ease;
}

.text-link:hover {
  color: #8a9bff;
  text-decoration: underline;
}

.add-team-button {
  /* Inherits general styles from .button */
  background-color: #4caf50; /* Green background */
  color: white;
  padding: 0.5rem 0.9rem; /* Added padding from .button */
  border-radius: 6px; /* Added border-radius from .button */
  font-size: 0.85rem; /* Added font-size from .button */
  font-weight: 600; /* Added font-weight from .button */
  cursor: pointer; /* Added cursor from .button */
  transition: all 0.2s ease; /* Added transition from .button */
  white-space: nowrap; /* Added white-space from .button */
}

.add-team-button:hover:not(:disabled) {
  background-color: #388e3c; /* Darker green on hover, matches .repack-all-button */
  opacity: 0.9; /* Added opacity from .button:hover */
  transform: translateY(-1px); /* Added transform from .button:hover */
}

/* Remove specific active state, use general .button:active */
/* .add-team-button:active {
  transform: translateY(0);
} */

.team-info-grid {
  display: grid;
  grid-template-columns: repeat(
    auto-fit,
    minmax(250px, 1fr)
  ); /* Flexible columns */
  gap: 1rem;
  margin-bottom: 1rem;
}

.team-info-item {
  background-color: #2a2a2a; /* Match card background or a slightly different shade */
  border-radius: 6px;
  padding: 0.75rem 1rem;
  word-break: break-word; /* Prevent long IDs from overflowing */
}

.team-info-item strong {
  display: inline-block; /* Ensure strong doesn't take full width */
  margin-right: 0.5rem;
  color: #aaa; /* Lighter color for labels */
  font-weight: 500;
}

.date-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}


/* Additional styles if needed for specific items */
.team-info-item strong[style='font-size: 0.8em;'] {
  color: #bbb; /* Slightly different color or style for smaller label */
}

.team-info-item strong[style='font-size: 0.9em;'] {
  color: #bbb; /* Slightly different color or style for smaller label */
}

/* Team page specific styles */
.section {
  margin-top: 2rem;
}


.role-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 600;
  text-transform: capitalize;
  background-color: #e2e8f0;
  color: #4a5568;
}


.text-muted {
  color: #a0aec0;
}

/* Danger zone styles - more compact and less red */
.danger-section {
  border: 1px solid #f59e0b;
  border-radius: 0.5rem;
  background-color: #451a03;
  padding: 1rem;
  margin-top: 2rem;
}

.danger-section h2 {
  color: #fbbf24;
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.danger-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1.5rem;
}

.danger-info {
  flex: 1;
}

.danger-info h3 {
  color: #fbbf24;
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.danger-info p {
  color: #fcd34d;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.deletion-summary {
  background-color: #78350f;
  padding: 0.75rem;
  border-radius: 0.25rem;
  border: 1px solid #f59e0b;
}

.deletion-summary h4 {
  color: #fbbf24;
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
}

.deletion-summary ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #fcd34d;
  font-size: 0.85rem;
}

.deletion-summary li {
  margin-bottom: 0.2rem;
}

.button-danger {
  background-color: #f59e0b;
  color: white;
  border: 1px solid #f59e0b;
}

.button-danger:hover {
  background-color: #d97706;
  border-color: #d97706;
}

.delete-confirmation {
  text-align: center;
}

.delete-confirmation p {
  color: #fbbf24;
  margin-bottom: 0.75rem;
  font-weight: 500;
  font-size: 0.9rem;
}

.confirmation-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}



.apk-repacker-component {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #444;
  border-radius: 6px;
  background-color: #2a2a2a;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.component-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #fff;
}

.asset-filename-container {
  margin-top: 10px;
}

.asset-filename-input {
  min-height: unset !important;
  height: auto !important;
  padding: 8px !important;
  margin-bottom: 0 !important;
  resize: none !important;
}

.asset-filename-help {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.form-actions {
  margin-top: 15px;
}

.download-actions {
  display: flex;
  gap: 10px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  padding: 0 20px;
}

.error-logo {
  width: 42px;
  height: auto;
  margin-bottom: 0.5rem;
}

.error-title {
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.error-message {
  font-size: 0.7rem;
  margin-bottom: 0.5rem;
  color: #aaa;
  max-width: 80%;
}