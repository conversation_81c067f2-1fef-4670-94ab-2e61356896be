import { supabase } from '$lib/server/supabase';

/** @type {import('./$types').PageServerLoad} */
export async function load({ parent }) {
  // Get parent data which includes authentication status
  await parent(); // This ensures layout authentication check runs first

  try {
    // Fetch overview statistics from the database
    const [
      teamsResult,
      devicesResult,
      walletsResult,
      transactionsResult,
      recentTeamsResult,
      totalBalanceResult,
      recentTransactionsResult
    ] = await Promise.all([
      // Total teams count
      supabase
        .from('teams')
        .select('*', { count: 'exact', head: true }),

      // Total devices count
      supabase
        .from('devices')
        .select('*', { count: 'exact', head: true }),

      // Total wallets count
      supabase
        .from('wallets')
        .select('*', { count: 'exact', head: true }),

      // Total transactions count and sum
      supabase
        .from('transactions')
        .select('amount'),

      // Recent teams (this month)
      supabase
        .from('teams')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString()),

      // Total balance across all teams
      supabase
        .from('teams')
        .select('balance'),

      // Recent transactions for activity metrics
      supabase
        .from('transactions')
        .select('amount, created_at')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(100)
    ]);

    // Calculate derived statistics
    const totalTeams = teamsResult.count || 0;
    const totalDevices = devicesResult.count || 0;
    const totalWallets = walletsResult.count || 0;
    const totalTransactions = transactionsResult.data?.length || 0;
    const newTeamsThisMonth = recentTeamsResult.count || 0;

    // Calculate total balance
    const totalBalance = totalBalanceResult.data?.reduce((sum, team) => {
      return sum + (parseFloat(team.balance) || 0);
    }, 0) || 0;

    // Calculate transaction volume
    const totalTransactionVolume = transactionsResult.data?.reduce((sum, tx) => {
      return sum + Math.abs(parseFloat(tx.amount) || 0);
    }, 0) || 0;

    // Calculate recent activity
    const recentTransactionVolume = recentTransactionsResult.data?.reduce((sum, tx) => {
      return sum + Math.abs(parseFloat(tx.amount) || 0);
    }, 0) || 0;

    // Calculate average balance per team
    const avgBalancePerTeam = totalTeams > 0 ? totalBalance / totalTeams : 0;

    // Calculate devices per team ratio
    const devicesPerTeam = totalTeams > 0 ? totalDevices / totalTeams : 0;

    return {
      stats: {
        totalTeams,
        totalDevices,
        totalWallets,
        totalTransactions,
        newTeamsThisMonth,
        totalBalance,
        totalTransactionVolume,
        recentTransactionVolume,
        avgBalancePerTeam,
        devicesPerTeam
      }
    };
  } catch (error) {
    console.error('Error fetching overview data:', error);

    // Return fallback data in case of error
    return {
      stats: {
        totalTeams: 0,
        totalDevices: 0,
        totalWallets: 0,
        totalTransactions: 0,
        newTeamsThisMonth: 0,
        totalBalance: 0,
        totalTransactionVolume: 0,
        recentTransactionVolume: 0,
        avgBalancePerTeam: 0,
        devicesPerTeam: 0
      },
      error: 'Failed to load overview data'
    };
  }
}
