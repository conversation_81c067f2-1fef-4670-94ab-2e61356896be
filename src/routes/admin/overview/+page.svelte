<script>
  /** @type {import('./$types').PageData} */
  export let data;

  // Helper function to format numbers
  function formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  // Helper function to format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }



  $: stats = data.stats || {};
  $: hasError = data.error;
</script>

<svelte:head>
  <title>Admin Overview</title>
</svelte:head>

{#if hasError}
  <div class="admin-card error-card">
    <div class="admin-card-title">Error Loading Data</div>
    <p>Unable to load overview statistics. Please check the database connection.</p>
  </div>
{/if}

<!-- System Status Card -->
<div class="admin-card">
  <div class="admin-card-title">
    Overview
  </div>

  <!-- Core Stats Summary -->
  <div class="admin-stats-compact">
    <div class="stats-summary">
      <div class="stat-item">
        <span class="stat-label">Total Teams:</span>
        <span class="stat-value-compact">{formatNumber(stats.totalTeams)}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Active Devices:</span>
        <span class="stat-value-compact" style="color: #4caf50;">{formatNumber(stats.totalDevices)}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Total Balance:</span>
        <span class="stat-value-compact">{formatCurrency(stats.totalBalance)}</span>
      </div>
    </div>
  </div>

  <!-- Usage Stats -->
  <div class="admin-stats-compact">
    <div class="stats-summary">
      <div class="stat-item">
        <span class="stat-label">Total Wallets:</span>
        <span class="stat-value-compact">{formatNumber(stats.totalWallets)}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Devices/Team:</span>
        <span class="stat-value-compact">{Math.round(stats.devicesPerTeam)}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Transactions:</span>
        <span class="stat-value-compact">{formatNumber(stats.totalTransactions)}</span>
      </div>
    </div>
  </div>

  <!-- Additional Stats -->
  <div class="admin-stats">
    <div class="stat-card">
      <div class="stat-label">New Teams This Month</div>
      <div class="stat-value">+{stats.newTeamsThisMonth}</div>
      <div class="stat-description">Teams created this month</div>
    </div>

    <div class="stat-card">
      <div class="stat-label">Average Balance</div>
      <div class="stat-value">{formatCurrency(stats.avgBalancePerTeam)}</div>
      <div class="stat-description">Per team average</div>
    </div>

    <div class="stat-card">
      <div class="stat-label">Transaction Volume</div>
      <div class="stat-value">{formatCurrency(stats.totalTransactionVolume)}</div>
      <div class="stat-description">Total transaction volume</div>
    </div>

    <div class="stat-card">
      <div class="stat-label">Recent Activity</div>
      <div class="stat-value">{formatCurrency(stats.recentTransactionVolume)}</div>
      <div class="stat-description">Last 30 days volume</div>
    </div>
  </div>
</div>

<style>
  .error-card {
    background-color: #ffebee;
    border-left: 4px solid #f44336;
  }

  .error-card p {
    color: #c62828;
    margin: 0;
  }
</style>